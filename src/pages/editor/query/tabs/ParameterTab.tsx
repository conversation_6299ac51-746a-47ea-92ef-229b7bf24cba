import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { StyledToast } from '@/components/styledToasterContainer';
import { GsqlQueryMeta, QueryParam } from '@tigergraph/tools-models';
import { KIND } from 'baseui/toast';
import QueryParamForm from '../params/QueryParamForm';
import { ParamErrors } from '@/utils/queryParam';

export interface ParameterTabProps {
  type: 'query' | 'restpp endpoints';
  pathParameters: QueryParam[];
  parameters: QueryParam[];
  pathPayload: Record<string, string>;
  payload: Record<any, any>;
  onPathParamChange: (param: QueryParam, value: any) => void;
  onParamChange: (param: QueryParam, value: any) => void;
  graphName: string;
  paramErrors: ParamErrors;
  query?: GsqlQueryMeta;
}

export default function ParameterTab({
  type,
  pathParameters,
  parameters,
  pathPayload,
  payload,
  onPathParamChange,
  onParamChange,
  graphName,
  paramErrors,
  query,
}: ParameterTabProps) {
  const [css, theme] = useStyletron();

  return (
    <>
      {type === 'restpp endpoints' && pathParameters.length > 0 && (
        <>
          <div className={css({ color: theme.colors['text.primary'], fontWeight: 600, marginBottom: '8px' })}>
            Path Parameters
          </div>
          <QueryParamForm
            queryParams={pathParameters}
            queryPayload={pathPayload}
            onQueryParamChange={onPathParamChange}
            graphName={graphName}
            paramErrors={{}}
          />
        </>
      )}

      <>
        {type === 'restpp endpoints' && (
          <div className={css({ color: theme.colors['text.primary'], fontWeight: 600, marginBottom: '8px' })}>
            API Parameters
          </div>
        )}
        <QueryParamForm
          queryParams={parameters}
          queryPayload={payload}
          onQueryParamChange={onParamChange}
          graphName={graphName}
          paramErrors={paramErrors}
        />
      </>

      {type === 'query' && !query?.installed && (
        <div className={css({ marginTop: '16px' })}>
          <StyledToast
            kind={KIND.info}
            closeable={false}
            message="The query is not installed and will be run in interpreted mode. Interpreted queries have limited feature support and may result in slower performance. For optimal execution, please install the query first."
          />
        </div>
      )}
    </>
  );
}
