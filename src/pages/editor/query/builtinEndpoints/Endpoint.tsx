import { useStyletron } from '@tigergraph/app-ui-lib/Theme';
import { useEffect, useState } from 'react';
import { ApiType } from '@/pages/editor/query/builtinEndpoints/type';
import { expand } from 'inline-style-expand-shorthand';
import { useTheme } from '@/contexts/themeContext';
import { GsqlParameter, QueryMetaLogic, QueryParam } from '@tigergraph/tools-models';
import HttpMethodBadge from '@/components/HttpMethodBadge';
import RunAPIDrawer from '@/pages/editor/query/RunAPIDrawer';
import { useWorkspaceContext } from '@/contexts/workspaceContext';

export interface EndpointProps {
  apiName: string;
  apiInfo: Record<string, GsqlParameter>;
  graphName: string;
}

export function Endpoint({ apiName, apiInfo, graphName }: EndpointProps) {
  const [css, theme] = useStyletron();
  const { currentWorkspace } = useWorkspaceContext();
  const [pathParams, setPathParams] = useState<QueryParam[]>([]);
  const [bodyParams, setBodyParams] = useState<QueryParam[]>([]);

  const [isRunDrawerOpen, setIsRunDrawerOpen] = useState(false);

  // set queryParams and pathParams
  useEffect(() => {
    const pattern = /{([^}]+)}/g;
    const matches = apiName.split(' ')[1].matchAll(pattern);
    let pathParams: QueryParam[] = [];
    for (const match of matches) {
      pathParams.push({
        paramName: match[1],
        paramType: { type: 'STRING' },
        paramDefaultValue: match[1] === 'graph_name' ? graphName : '',
      });
    }
    setPathParams(pathParams);

    // the parameters in apiInfo.parameters except path parameters are query parameters
    const filteredParams: Record<string, GsqlParameter> = {};
    if (apiInfo) {
      for (const param of Object.keys(apiInfo)) {
        if (!pathParams.find((p) => p.paramName === param)) {
          filteredParams[param] = apiInfo[param];
        }
      }
      const queryParams = QueryMetaLogic.convertGSQLParameters(filteredParams);
      setBodyParams(queryParams);
    }
  }, [apiInfo, apiName, graphName]);

  const apiType: ApiType = apiName.split(' ')[0] as ApiType;
  const apiPath = apiName.split(' ')[1];

  const { themeType } = useTheme();

  const backgroundColorMap = {
    [ApiType.GET]: themeType === 'light' ? 'rgba(37, 131, 222, 0.10)' : 'rgba(37, 131, 222, 0.10)',
    [ApiType.POST]: themeType === 'light' ? 'rgba(39, 186, 63, 0.10)' : 'rgba(39, 186, 63, 0.10)',
    [ApiType.PUT]: themeType === 'light' ? 'rgba(248, 173, 104, 0.15)' : 'rgba(248, 173, 104, 0.15)',
    [ApiType.DELETE]: themeType === 'light' ? 'rgba(214, 69, 69, 0.10)' : 'rgba(214, 69, 69, 0.10)',
  };

  const colorMap = {
    [ApiType.GET]: theme.colors['background.informative.subtle'],
    [ApiType.POST]: theme.colors['background.success.bold'],
    [ApiType.PUT]: theme.colors['background.brand.bold'],
    [ApiType.DELETE]: theme.colors['background.danger.subtle'],
  };

  return (
    <div
      className={css({
        backgroundColor: backgroundColorMap[apiType],
        marginBottom: '10px',
        padding: '8px',
        borderRadius: '2px',
        ...expand({
          border: `1px solid ${colorMap[apiType]}`,
        }),
        cursor: 'pointer',
      })}
      onClick={() => setIsRunDrawerOpen(true)}
    >
      <div
        key={apiName}
        className={css({
          display: 'flex',
          justifyContent: 'space-between',
          height: '24px',
          alignItems: 'center',
        })}
      >
        <div
          className={css({
            display: 'flex',
            alignItems: 'center',
            maxWidth: '95%',
            gap: '8px',
          })}
        >
          <HttpMethodBadge apiType={apiType} />
          <div
            className={css({
              maxWidth: '100%',
              overflow: 'auto',
            })}
          >
            {apiName.split(' ')[1]}
          </div>
        </div>
        <RunAPIDrawer
          isOpen={isRunDrawerOpen}
          onClose={() => setIsRunDrawerOpen(false)}
          type="restpp endpoints"
          wp={currentWorkspace!}
          graphName={graphName}
          path={`/restpp${apiPath}`}
          method={apiType}
          pathParameters={pathParams}
          bodyParameters={bodyParams}
          name={apiPath}
        />
      </div>
    </div>
  );
}
