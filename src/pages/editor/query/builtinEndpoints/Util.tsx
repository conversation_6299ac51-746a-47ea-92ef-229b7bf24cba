import { styled } from '@tigergraph/app-ui-lib/Theme';

export const Heading = styled('div', () => ({
  fontSize: '14px',
  lineHeight: '16px',
  fontWeight: 600,
  marginBottom: '8px',
}));

export const ReadyApi = [
  'DELETE /graph/{graph_name}/delete_by_type/vertices/{vertex_type}/',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}',
  'DELETE /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}',
  'DELETE /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}',
  'GET /abortquery/{graph_name}',
  'GET /data_consistency_check',
  'GET /deleted_vertex_check/{graph_name}',
  'GET /endpoints/{graph_name}',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}',
  'GET /graph/{graph_name}/edges/{source_vertex_type}/{source_vertex_id}/{edge_type}/{target_vertex_type}/{target_vertex_id}/{discriminator}',
  'GET /graph/{graph_name}/vertices/{vertex_type}/{vertex_id}',
  'GET /query_result/{requestid}',
  'GET /rebuildnow/{graph_name}',
  'GET /showdelayedlistall',
  'GET /showprocesslist/{graph_name}',
  'GET /showprocesslistall',
  'GET /statistics/{graph_name}',
  'POST /ddl/{graph_name}',
];
